import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DialogModule, Dialog } from '@angular/cdk/dialog';
import { SavedViewsService } from '../../../core/services/saved-views.service';
import { HttpClientModule } from '@angular/common/http';
import { ListToolbarComponent } from '../../../shared/components/list-toolbar/list-toolbar.component';
import { GenericTableComponent } from '../../../shared/components/generic-table/generic-table.component';
import { OFRequest } from '../../../core/models/of-request';
import { OfService } from '../../../core/services/request_of.service';
import { BaseEntityListComponent } from '../../../shared/components/base/base-list.component';
import { OfRequestsFormComponent } from '../of-requests-form/of-requests-form.component';

@Component({
  selector: 'app-of-requests-list',
  standalone: true,
  imports: [CommonModule, DialogModule, HttpClientModule, ListToolbarComponent, GenericTableComponent],
  templateUrl: './of-requests-list.component.html',
  styleUrls: ['./of-requests-list.component.scss'],
})
export class OfRequestsListComponent
  extends BaseEntityListComponent<OFRequest> {

  /** Dialog form shown when creating / editing a row */
  editComponent = OfRequestsFormComponent;
  /** Used for localStorage keys and titles */
  tableName = 'OF Request Table';
  requestType = 'OF'
  requestTypeLong = 'Outside Finishing';

  /** Column defs */
  displayedColumns: (keyof OFRequest | 'actions')[] = [
    'actions',
    'of_id',
    'job_number',
    'change_orders',
    'customer_name',
    'job_description',
    'outside_finishing_request_description',
    'status',
    'due_date_for_outside_finishing',
    'final_quantity',
    'outside_finisher',
    'work_type_needed',
    'notes',
    'created_by',
    'created_on',
    'last_modified_by',
    'last_modified_on',
  ];

  constructor(public thisService: OfService, dialog: Dialog, savedViewsService: SavedViewsService) {
    super(dialog, savedViewsService);          // forward services to the base class
  }

  onVisibleRows(rows: Array<OFRequest | any>): void {
      this.tempArray = rows.filter(r => !r.__group && !r.__detail);
    }
}
