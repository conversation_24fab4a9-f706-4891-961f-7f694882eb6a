<!-- header -->
<app-list-toolbar
    [savedViews]="savedViews"
    [tempArrayLength]="tempArray.length"
    [requestType]="requestType"
    [lastUpdatedTime]="lastUpdatedTime"
    (create)="openNewItem()"
    (openSettings)="openTableSettingsFromCurrent()"
    [showSaveAsViewButton]="true"
    [savedCustomViews]="savedCustomViews"
    [currentAppliedView]="currentAppliedView"
    (saveAsView)="onSaveAsView()"
    (customViewSelected)="onCustomViewSelected($event)"
    (deleteView)="onDeleteView($event)"
    (viewSelected)="onViewSelected($event)">
</app-list-toolbar>
  <!-- header end -->

<!-- table start -->
<app-generic-table
    #genericTable
    [requestType]="requestType"
    [tableName]="tableName"
    [displayedColumns]="displayedColumns"
    [dataService]="thisService"
    [editComponent]="editComponent"  
    [showDetailColumn]=false
    [showInfoBtn]=true
    [showPrintBtn]=true
    (dataLoaded)="onDataLoaded($event)"
    (viewsLoaded)="onViewsLoaded($event)"
    (savedViewId) = "onSavedViewID($event)"
    >
</app-generic-table> 
<!-- table end -->