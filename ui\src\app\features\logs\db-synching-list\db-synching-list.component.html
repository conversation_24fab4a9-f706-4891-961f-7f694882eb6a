
<app-list-toolbar
  [savedViews]="savedViews"
  [isRequestTable]="false"
  [tempArrayLength]="tempArray.length"
  [requestType]="requestType"
  [lastUpdatedTime]="lastUpdatedTime"
  (create)="openNewItem()"
  (openSettings)="openTableSettingsFromCurrent()"
  [showSaveAsViewButton]="true"
  [savedCustomViews]="savedCustomViews"
  [currentAppliedView]="currentAppliedView"
  (saveAsView)="onSaveAsView()"
  (customViewSelected)="onCustomViewSelected($event)"
  (deleteView)="onDeleteView($event)"
  (viewSelected)="onViewSelected($event)">
</app-list-toolbar>
  <!-- header end -->

<div class="flex gap-[5px] p-5 pl-8">
  <button
    *ngFor="let row of tempArray"
    [ngClass]="row.sync_status === 'running'
                ? 'bg-green-200 hover:bg-green-300'
                : 'bg-red-200  hover:bg-red-300'"
    class="text-gray-800 font-semibold py-2 px-4 border border-gray-400 rounded shadow"
    (click)="onToggleSync(row)"
  >
    {{ row.sync_status === 'running'
         ? 'Stop '  + row.human_readable_table_name + ' Sync'
         : 'Start ' + row.human_readable_table_name + ' Sync' }}
  </button>
</div>


<!-- table start -->
    <app-generic-table
        #genericTable
        [requestType]="requestType"
        [tableName]="tableName"
        [displayedColumns]="displayedColumns"
        [dataService]="thisService"
        [showDetailColumn]="false"
        [showEdit]="false"
        (dataLoaded)="onDataLoaded($event)"
        (viewsLoaded)="onViewsLoaded($event)"
        (visibleDataChange)="onVisibleRows($event)"
        (savedViewId) = "onSavedViewID($event)"
        >
    </app-generic-table> 
<!-- table end -->

