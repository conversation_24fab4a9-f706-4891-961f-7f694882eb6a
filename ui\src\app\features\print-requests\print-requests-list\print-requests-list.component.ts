import { Component  } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DialogModule, Dialog } from '@angular/cdk/dialog';
import { SavedViewsService } from '../../../core/services/saved-views.service';
import { HttpClientModule } from '@angular/common/http';
import { ListToolbarComponent } from '../../../shared/components/list-toolbar/list-toolbar.component';
import { GenericTableComponent } from '../../../shared/components/generic-table/generic-table.component';
import { PrintRequest } from '../../../core/models/print-request';
import { PrintService } from '../../../core/services/request_pr.service';
import { BaseEntityListComponent } from '../../../shared/components/base/base-list.component';
import { PrintRequestsFormComponent } from '../print-requests-form/print-requests-form.component';
import { TableSettingsService } from '../../../core/services/table-settings.service';

@Component({
  selector: 'app-print-requests-list',
  standalone: true,
  imports: [CommonModule, DialogModule, HttpClientModule, GenericTableComponent, ListToolbarComponent],
  templateUrl: './print-requests-list.component.html',
  styleUrl: './print-requests-list.component.scss',
  providers: [TableSettingsService] 
})



export class PrintRequestsListComponent 
  extends BaseEntityListComponent<PrintRequest> {

  /** Dialog form shown when creating / editing a row */
  editComponent = PrintRequestsFormComponent;
  /** Used for localStorage keys and titles */
  tableName = 'PR Request Table';
  requestType = 'PR'
  requestTypeLong = 'Print'

  displayedColumns: (keyof PrintRequest | 'actions')[] = 
  [ 'actions',
    'print_id',
    'job_number',
    'change_orders',
    'customer_name',
    'work_type_needed',
    'job_description',
    'print_request_description' ,
    'status',
    'due_date_for_print',
    'number_of_forms',
    'paper',
    'inks',
    'notes',
    'created_by',
    'created_on',
    'last_modified_by',
    'last_modified_on'
  ];

  constructor(public thisService: PrintService, dialog: Dialog, savedViewsService: SavedViewsService) {
    super(dialog, savedViewsService);          // forward services to the base class
   }

   onVisibleRows(rows: Array<PrintRequest | any>): void {
      this.tempArray = rows.filter(r => !r.__group && !r.__detail);
    }
}
