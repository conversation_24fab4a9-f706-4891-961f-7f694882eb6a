// src/app/shared/components/list-toolbar/list-toolbar.component.ts
import { Component, EventEmitter, Input, Output, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TableSettingsFromDB } from '../../../core/models/table-settings';

@Component({
  selector: 'app-list-toolbar',
  imports: [CommonModule],
  templateUrl: './list-toolbar.component.html',
})
export class ListToolbarComponent {

  /* ---------- Inputs ---------- */
  @Input() newLabel          = 'New Item';
  @Input() savedViews: TableSettingsFromDB[] = [];
  @Input() selectedViewId: number | null = null;
  @Input() tempArrayLength   = 0;
  @Input() requestType       = '';
  @Input() lastUpdatedTime   = '';
  /* Optional - to hide btn 'new sync' when is false*/
  @Input() isRequestTable: boolean = true;
  @Input() savedCustomViews: any[] = [];
  @Input() showSaveAsViewButton: boolean = false;
  @Input() currentAppliedView: string = '';

  /* ---------- Outputs ---------- */
  @Output() create       = new EventEmitter<void>();
  @Output() openSettings = new EventEmitter<void>();
  @Output() saveAsView   = new EventEmitter<void>();
  @Output() viewSelected = new EventEmitter<Event>();
  @Output() customViewSelected = new EventEmitter<string>();
  @Output() deleteView = new EventEmitter<any>();

  /* ---------- Component state ---------- */
  isSavedViewsOpen = false;
  isAdminViewsOpen = false;

  /* ---------- internal helper ---------- */
  onSelect(event: Event): void {
    this.viewSelected.emit(event);   // emit the raw DOM event
  }

  onSaveAsViewClick(): void {
    console.log('Save as view button clicked in list-toolbar');
    this.saveAsView.emit();
  }

  onSavedViewSelect(viewName: string): void {
    if (viewName) {
      this.customViewSelected.emit(viewName);
    }
    // Close dropdown after selection
    this.isSavedViewsOpen = false;
  }

  toggleSavedViewsDropdown(): void {
    this.isSavedViewsOpen = !this.isSavedViewsOpen;
    // Close the other dropdown
    this.isAdminViewsOpen = false;
  }

  onDeleteView(view: any, event: Event): void {
    // Stop event propagation to prevent dropdown from closing
    event.stopPropagation();

    // Show confirmation dialog
    const confirmed = confirm(`Are you sure you want to delete the view "${view.name}"?`);

    if (confirmed) {
      console.log('🗑️ Deleting view:', view);
      this.deleteView.emit(view);
    }
  }

  toggleTableSettingsDropdown(): void {
    this.isAdminViewsOpen = !this.isAdminViewsOpen;
    // Close the other dropdown
    this.isSavedViewsOpen = false;
  }

  onTableSettingSelect(settingsId: string): void {
    // Update selectedViewId immediately for UI feedback
    this.selectedViewId = settingsId ? Number(settingsId) : null;

    // Create a fake event for backward compatibility
    const fakeEvent = {
      target: { value: settingsId }
    } as any;

    this.onSelect(fakeEvent);
    this.isAdminViewsOpen = false;
  }

  getSelectedViewName(): string {
    // If no selectedViewId, check for Default view
    if (!this.selectedViewId) {
      const defaultView = this.savedViews.find(view => view.settings_name === 'Default');
      return defaultView ? (defaultView.settings_name || '') : '';
    }

    // Find view by selectedViewId
    const selectedView = this.savedViews.find(view => view.settings_id === this.selectedViewId);
    return selectedView ? (selectedView.settings_name || '') : '';
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    // Close dropdowns when clicking outside
    const target = event.target as HTMLElement;
    if (!target.closest('.relative')) {
      this.isSavedViewsOpen = false;
      this.isAdminViewsOpen = false;
    }
  }
}
