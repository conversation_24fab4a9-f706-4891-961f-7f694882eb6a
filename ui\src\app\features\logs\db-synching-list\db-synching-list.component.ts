import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DialogModule, Dialog } from '@angular/cdk/dialog';
import { SavedViewsService } from '../../../core/services/saved-views.service';
import { HttpClientModule } from '@angular/common/http';
import { ListToolbarComponent } from '../../../shared/components/list-toolbar/list-toolbar.component';
import { GenericTableComponent } from '../../../shared/components/generic-table/generic-table.component';
import { SyncStatus, SyncServiceStatus } from '../../../core/models/db_sync';
import { DBSyncService } from '../../../core/services/db_sync.service'
import { BaseEntityListComponent } from '../../../shared/components/base/base-list.component';
import { TagsRequestsListComponent } from '../../tag-requests/tags-requests-list/tags-requests-list.component';
import { ComponentType } from '@angular/cdk/overlay';
import { TableSettingsService } from '../../../core/services/table-settings.service';

@Component({
  selector: 'app-db-synching-list',
  standalone: true,
  imports: [CommonModule, DialogModule, HttpClientModule, ListToolbarComponent, GenericTableComponent],
  templateUrl: './db-synching-list.component.html',
  styleUrl: './db-synching-list.component.scss',
  providers: [TableSettingsService] 
})
export class DbSynchingListComponent 
   extends BaseEntityListComponent<SyncStatus> { 

  DbSynchingListComponent = DbSynchingListComponent;

  editComponent = DbSynchingListComponent;

  tableName = 'Sync Status Table';
  requestType = 'Synching'

    displayedColumns: (keyof SyncStatus | 'actions')[] = [
      'actions',
      'human_readable_table_name',
      'table_name',
      'last_synced',
      'sync_type',
      'sync_status',
    ];

    
  
    constructor(public thisService: DBSyncService, dialog: Dialog, savedViewsService: SavedViewsService ) {
      super(dialog, savedViewsService)
    }

    onVisibleRows(rows: Array<SyncStatus | any>): void {
        // strip out group-header / detail rows before counting
      this.tempArray = rows.filter(r => !r.__group && !r.__detail);
    }

    sync_service_status: SyncServiceStatus = <SyncServiceStatus>{
        table_name: '',
        current_status: ''
            
        }

    onToggleSync(row: SyncStatus){
      console.log(this.tempArray)
      this.sync_service_status.table_name = row.table_name
      this.sync_service_status.current_status = row.sync_status
      console.log("This is row: ", row)
      this.thisService.startStopSync(this.sync_service_status)
    }

}
