
<!-- header -->
<app-list-toolbar
  [savedViews]="savedViews"
  [tempArrayLength]="tempArray.length"
  [requestType]="requestType"
  [lastUpdatedTime]="lastUpdatedTime"
  (create)="openNewItem()"
  (openSettings)="openTableSettingsFromCurrent()"
  [showSaveAsViewButton]="true"
  [savedCustomViews]="savedCustomViews"
  [currentAppliedView]="currentAppliedView"
  (saveAsView)="onSaveAsView()"
  (customViewSelected)="onCustomViewSelected($event)"
  (deleteView)="onDeleteView($event)"
  (viewSelected)="onViewSelected($event)">
</app-list-toolbar>
  <!-- header end -->
<!-- table start -->
    <app-generic-table
        #genericTable
        [requestType]="requestType"
        [tableName]="tableName"
        [displayedColumns]="displayedColumns"
        [dataService]="thisService"
        [editComponent]="editComponent"  
        [detailComponent]="SephoraImportsListComponent"
        [rowKeyFn]="rowKeyFn"
        [extraProvidersFn]="extraProviders" 
        (dataLoaded)="onDataLoaded($event)"
        (viewsLoaded)="onViewsLoaded($event)"
        (visibleDataChange)="onVisibleRows($event)"
        (savedViewId) = "onSavedViewID($event)"
        >
    </app-generic-table> 
<!-- table end -->


<!-- <div class="multi-table">
    <ng-container *ngComponentOutlet="SephoraImportsListComponent"></ng-container>
</div> -->
<app-sticky-scroll></app-sticky-scroll>
