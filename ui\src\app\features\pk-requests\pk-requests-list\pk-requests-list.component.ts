import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DialogModule, Dialog } from '@angular/cdk/dialog';
import { SavedViewsService } from '../../../core/services/saved-views.service';
import { HttpClientModule } from '@angular/common/http';
import { ListToolbarComponent } from '../../../shared/components/list-toolbar/list-toolbar.component';
import { GenericTableComponent } from '../../../shared/components/generic-table/generic-table.component';
import { PackoutRequest } from '../../../core/models/pk-request';
import { PkService } from '../../../core/services/request_pk.service';
import { BaseEntityListComponent } from '../../../shared/components/base/base-list.component';
import { PkRequestsFormComponent } from '../pk-requests-form/pk-requests-form.component';
import { TableSettingsService } from '../../../core/services/table-settings.service';

@Component({
  selector: 'app-pk-requests-list',
  standalone: true,
  imports: [CommonModule, DialogModule, HttpClientModule, ListToolbarComponent, GenericTableComponent],
  templateUrl: './pk-requests-list.component.html',
  styleUrl: './pk-requests-list.component.scss',
  providers: [TableSettingsService] 
})
export class PkRequestsListComponent
  extends BaseEntityListComponent<PackoutRequest> {

  /** Dialog form shown when creating / editing a row */
  editComponent = PkRequestsFormComponent;

  /** Used for localStorage keys and titles */
  tableName = 'Packout Request Table';
  requestType = 'Packout'

  /** Column defs */
  displayedColumns: (keyof PackoutRequest | 'actions')[] = 
  [ 'actions',
    'request_pk_id',
    'request_pk_ship_date',
    'request_pk_created_on',
    'job_client_name',
    'pm',
    'job_number_description',
    'request_pk_status',
    'request_lf',
    'request_pk_change_orders',
    'request_pk_created_by',
    'request_pk_description',
    'request_pk_hw',
    'request_pk_instruction_sheets',
    'request_pk_job_number',
    'request_pk_last_modified_by',
    'request_pk_last_modified_on',
    'request_pk_notes',
    'request_pk_num_graphics_per',
    'request_pk_outside_finishing',
    'request_pk_packing',
    'request_pk_packing_materials',
    'request_pk_packing_materials_notes',
    'request_pk_packout_estimate',
    'request_pk_packout_info_confirmed',
    'request_pk_packout_info_seenotes',
    'request_pk_packout_info_uploaded',
    'request_pk_packout_location',
    'request_pk_restricted_packout_size',
    'request_pk_shipping_location',
    'request_pk_special_labelling',
    'request_pk_total_fixtures',
    'request_pk_total_packout'];

  constructor(public thisService: PkService, dialog: Dialog, savedViewsService: SavedViewsService) {
    super(dialog, savedViewsService);          // forward services to the base class
  }
}
