import pkg from 'pg';
const { Pool } = pkg;
import { pgConfig } from '../../config.js';

/**
 * Database functions for saved_views table
 * Handles user's custom table views (sort, grouping, filters)
 */

/**
 * Get all saved views for a user and table
 * @param {Pool} pool - PostgreSQL connection pool 
 * @param {string} userName - User name
 * @param {string} tableName - Table name (e.g., "PE Request Table")
 * @returns {Array} Array of saved views
 */
export async function getSavedViews(pool, userName, tableName) {
    const pgPool = new Pool(pgConfig);
    const pgClient = await pgPool.connect();

    try {
        const query = `
            SELECT id, view_name, sort_state, grouping_state, filters_state, created_at, updated_at
            FROM saved_views
            WHERE user_name = $1 AND table_name = $2
            ORDER BY created_at DESC
        `;

        console.log(`🔍 Getting saved views for user: ${userName}, table: ${tableName}`);
        const result = await pgClient.query(query, [userName, tableName]);
        console.log(`✅ Found ${result.rows.length} saved views`);

        return result.rows;
    } catch (err) {
        console.error('Error in getSavedViews:', err);
        throw err;
    } finally {
        pgClient.release();
    }
}

/**
 * Create a new saved view
 * @param {Object} pool - PostgreSQL connection pool
 * @param {Object} viewData - View data object
 * @param {string} viewData.userName - User name
 * @param {string} viewData.tableName - Table name
 * @param {string} viewData.viewName - View name
 * @param {Object} viewData.sortState - Sort state object
 * @param {string} viewData.groupingState - Grouping state string
 * @param {Object} viewData.filtersState - Filters state object
 * @returns {Object} Created view with ID
 */
export async function createSavedView(pool, viewData) {
    const pgPool = new Pool(pgConfig);
    const pgClient = await pgPool.connect();

    try {
        const { userName, tableName, viewName, sortState, groupingState, filtersState } = viewData;

        console.log('📝 Creating saved view:', viewData);

        // Check if view name already exists for this user/table
        const checkQuery = `
            SELECT id FROM saved_views
            WHERE user_name = $1 AND table_name = $2 AND view_name = $3
        `;
        const existing = await pgClient.query(checkQuery, [userName, tableName, viewName]);

        if (existing.rows.length > 0) {
            throw new Error(`View "${viewName}" already exists for this table`);
        }

        const insertQuery = `
            INSERT INTO saved_views (user_name, table_name, view_name, sort_state, grouping_state, filters_state)
            VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING id, view_name, sort_state, grouping_state, filters_state, created_at, updated_at
        `;

        const values = [
            userName,
            tableName,
            viewName,
            JSON.stringify(sortState),
            groupingState,
            JSON.stringify(filtersState)
        ];

        console.log('📊 Insert values:', values);
        const result = await pgClient.query(insertQuery, values);
        console.log('✅ View created successfully:', result.rows[0]);

        return result.rows[0];
    } catch (err) {
        console.error('Error in createSavedView:', err);
        throw err;
    } finally {
        pgClient.release();
    }
}

/**
 * Update an existing saved view
 * @param {Object} pool - PostgreSQL connection pool
 * @param {number} viewId - View ID
 * @param {string} userName - User name (for security)
 * @param {Object} updateData - Data to update
 * @returns {Object} Updated view
 */
async function updateSavedView(pool, viewId, userName, updateData) {
    const { viewName, sortState, groupingState, filtersState } = updateData;
    
    const query = `
        UPDATE saved_views 
        SET view_name = $1, sort_state = $2, grouping_state = $3, filters_state = $4, updated_at = CURRENT_TIMESTAMP
        WHERE id = $5 AND user_name = $6
        RETURNING id, view_name, sort_state, grouping_state, filters_state, created_at, updated_at
    `;
    
    try {
        const result = await pool.query(query, [
            viewName,
            JSON.stringify(sortState),
            groupingState,
            JSON.stringify(filtersState),
            viewId,
            userName
        ]);
        
        if (result.rows.length === 0) {
            throw new Error('View not found or access denied');
        }
        
        return result.rows[0];
    } catch (error) {
        console.error('Error updating saved view:', error);
        throw error;
    }
}

/**
 * Delete a saved view
 * @param {Pool} pool - PostgreSQL connection pool (not used, we create our own)
 * @param {number} viewId - View ID
 * @param {string} userName - User name (for security)
 * @returns {boolean} Success status
 */
export async function deleteSavedView(pool, viewId, userName) {
    const pgPool = new Pool(pgConfig);
    const pgClient = await pgPool.connect();

    try {
        console.log(`🗑️ Deleting view ID: ${viewId} for user: ${userName}`);

        const deleteQuery = `
            DELETE FROM saved_views
            WHERE id = $1 AND user_name = $2
            RETURNING id
        `;

        const result = await pgClient.query(deleteQuery, [viewId, userName]);
        const success = result.rows.length > 0;

        console.log(success ? '✅ View deleted successfully' : '❌ View not found or access denied');
        return success;
    } catch (err) {
        console.error('Error in deleteSavedView:', err);
        throw err;
    } finally {
        pgClient.release();
    }
}

/**
 * Get a specific saved view by ID
 * @param {Object} pool - PostgreSQL connection pool
 * @param {number} viewId - View ID
 * @param {string} userName - User name (for security)
 * @returns {Object} View data
 */
async function getSavedViewById(pool, viewId, userName) {
    const query = `
        SELECT id, view_name, sort_state, grouping_state, filters_state, created_at, updated_at
        FROM saved_views 
        WHERE id = $1 AND user_name = $2
    `;
    
    try {
        const result = await pool.query(query, [viewId, userName]);
        if (result.rows.length === 0) {
            throw new Error('View not found or access denied');
        }
        return result.rows[0];
    } catch (error) {
        console.error('Error getting saved view by ID:', error);
        throw error;
    }
}

// Functions are exported individually above
