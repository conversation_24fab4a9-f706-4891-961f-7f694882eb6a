import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DialogModule, Dialog } from '@angular/cdk/dialog';
import { SavedViewsService } from '../../../core/services/saved-views.service';
import { HttpClientModule } from '@angular/common/http';
import { ListToolbarComponent } from '../../../shared/components/list-toolbar/list-toolbar.component';
import { GenericTableComponent } from '../../../shared/components/generic-table/generic-table.component';
import { ShipRequest } from '../../../core/models/ship-request';
import { ShService } from '../../../core/services/request_sh.service';
import { BaseEntityListComponent } from '../../../shared/components/base/base-list.component';
import { ShipRequestsFormComponent } from '../ship-requests-form/ship-requests-form.component';
import { TableSettingsService } from '../../../core/services/table-settings.service';

@Component({
  selector: 'app-ship-requests-list',
  standalone: true,
  imports: [CommonModule, DialogModule, HttpClientModule, ListToolbarComponent, GenericTableComponent],
  templateUrl: './ship-requests-list.component.html',
  styleUrl: './ship-requests-list.component.scss',
  providers: [TableSettingsService] 
})
export class ShipRequestsListComponent 
  extends BaseEntityListComponent<ShipRequest> {
  /** Dialog form shown when creating / editing a row */
  editComponent = ShipRequestsFormComponent;
  /** Used for localStorage keys and titles */
  tableName = 'Shipping Request Table';
  requestType = 'Shipping'

    /** Column defs */
  displayedColumns: (keyof ShipRequest | 'actions')[] = 
  [ 'actions',
    'request_sh_id',
    'request_sh_ship_date',
    'request_sh_created_on',
    'job_client_name',
    'pm',
    'job_description',
    'request_sh_status',
    'request_sh_arrival_date',
    'request_sh_cos',
    'request_sh_created_by',
    'request_sh_description',
    'request_sh_destination',
    'request_sh_distro_list',
    'request_sh_distro_status',
    'request_sh_distro_type',
    'request_sh_job_number',
    'request_sh_last_modified_by',
    'request_sh_last_modified_on',
    'request_sh_notes',
    'request_sh_num_of_ship_and_location',
    'request_sh_outside_courier',
    'request_sh_package_type',
    'request_sh_packout_status_after_ship',
    'request_sh_pk_requests',
    'request_sh_ship_category',
    'request_sh_shipment_type',
    'request_sh_shipping_from',
    'request_sh_type'
  ];

  constructor(public thisService: ShService, dialog: Dialog, savedViewsService: SavedViewsService) {
    super(dialog, savedViewsService);          // forward services to the base class
  }
}
